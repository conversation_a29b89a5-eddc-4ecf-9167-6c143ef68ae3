import React from "react";
import { useLocation } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import Header from "./Header";
import AuthenticatedHeader from "./AuthenticatedHeader";

const HeaderWrapper = () => {
  const location = useLocation();
  const { currentUser } = useAuth();

  // Define pages that should NOT show any header (they handle their own headers)
  const noHeaderPages = ["/login", "/register", "/setup", "/chat"];

  // Check if current page should not show header
  const shouldShowNoHeader = noHeaderPages.includes(location.pathname);

  // Don't show header for specific pages that handle their own headers
  if (shouldShowNoHeader) {
    return null;
  }

  // Show authenticated header for protected pages when user is authenticated
  // Show public header for other cases (including Starter page)
  if (
    currentUser &&
    (location.pathname === "/admin" || location.pathname === "/student")
  ) {
    return <AuthenticatedHeader />;
  } else {
    return <Header />;
  }
};

export default HeaderWrapper;
