from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import google.generativeai as genai
import os
from dotenv import load_dotenv
load_dotenv()


# Initialize the FastAPI app
app = FastAPI()

# Allow frontend access
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Configure Gemini API
api_key = os.getenv("GEMINI_API_KEY")
if not api_key:
    raise ValueError("GEMINI_API_KEY not set in environment.")

genai.configure(api_key=api_key)
model = genai.GenerativeModel("gemini-2.0-flash-exp")

# Pydantic model for input
class ChatRequest(BaseModel):
    message: str

@app.post("/chat")
async def chat_endpoint(request: ChatRequest):
    try:
        prompt = f"""
        You are a friendly, supportive AI counselor conducting an assessment conversation with a child aged 6-8.
        Ask meaningful questions related to confidence, leadership, and creativity.

        The child's response: "{request.message}"
        
        Please respond with your next friendly, age-appropriate question or feedback.
        """
        response = model.generate_content(prompt)
        return {"response": response.text.strip()}
    except Exception as e:
        return {"response": "Oops! Something went wrong. Please try again later."}

# Run with: uvicorn gemini_api:app --reload
