from flask import Flask, request, jsonify
from flask_cors import CORS
import sys
import os
import traceback
import json

app = Flask(_name_)
CORS(app)  # Enable CORS for all routes

# Initialize the sentiment predictor
predictor = None

def load_sentiment_model():
    """Load the sentiment analysis model with proper error handling"""
    global predictor
    
    try:
        # Add the Sentiment-Analysis directory to the path
        sentiment_dir = os.path.join(os.path.dirname(_file_), 'Sentiment-Analysis')
        sys.path.append(sentiment_dir)
        
        # Check if the model file exists
        model_path = os.path.join(sentiment_dir, 'enhanced_sentiment_model.pkl')
        if not os.path.exists(model_path):
            print(f"Error: Model file not found at {model_path}")
            return False
        
        print(f"Loading model from: {model_path}")
        
        # Import the predictor class
        from enhanced_sentiment_predictor import EnhancedSentimentPredictor
        
        # Initialize the predictor
        predictor = EnhancedSentimentPredictor(model_path)
        print("Sentiment model loaded successfully!")
        return True
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please make sure all required packages are installed:")
        print("pip install transformers torch scipy numpy pandas python-dotenv")
        return False
    except Exception as e:
        print(f"Error loading sentiment model: {e}")
        print("Full traceback:")
        traceback.print_exc()
        return False

# Load the model when the server starts
print("Initializing sentiment analysis server...")
if not load_sentiment_model():
    print("Warning: Model could not be loaded. Server will start but predictions will fail.")

@app.route('/predict', methods=['POST'])
def predict_sentiment():
    try:
        print("Received prediction request")
        data = request.get_json()
        
        if not data:
            print("No JSON data received")
            return jsonify({'error': 'No JSON data provided'}), 400
        
        message = data.get('message', '')
        print(f"Message received: {message}")
        
        if not message:
            print("Empty message received")
            return jsonify({'error': 'No message provided'}), 400
        
        if predictor is None:
            print("Predictor is None - model not loaded")
            return jsonify({'error': 'Model not loaded. Please check server logs.'}), 500
        
        # Analyze the sentiment
        print("Analyzing sentiment...")
        results_tuple = predictor.analyze_sentiment(message)
        print(f"Analysis results tuple: {results_tuple}")
        
        # The analyze_sentiment method returns (summary, df) from create_results_table
        summary, df = results_tuple
        
        # Extract sentiment and emotion from the DataFrame
        sentiment = "Unknown"
        emotion = "Unknown"
        flag = "NONE"
        
        try:
            # Look for sentiment and emotion in the DataFrame
            for index, row in df.iterrows():
                if row['Analysis Type'] == 'Sentiment':
                    sentiment = row['Result']
                elif row['Analysis Type'] == 'Emotion':
                    emotion = row['Result']
                elif row['Analysis Type'] == 'Flag Status':
                    flag = row['Result']
        except Exception as e:
            print(f"Error extracting data from DataFrame: {e}")
            # Fallback: try to extract from summary
            if 'POSITIVE' in summary.upper():
                sentiment = 'POSITIVE'
            elif 'NEGATIVE' in summary.upper():
                sentiment = 'NEGATIVE'
            elif 'NEUTRAL' in summary.upper():
                sentiment = 'NEUTRAL'
        
        # Create a user-friendly response
        if flag == "URGENT_FLAG":
            response_text = f"I'm concerned about what you're sharing. Please consider talking to a trusted adult, teacher, or counselor. You're not alone, and there are people who want to help you."
        elif flag == "FLAG":
            response_text = f"I notice you might be feeling {emotion.lower()}. It's okay to feel this way. Would you like to talk more about what's on your mind?"
        elif sentiment.lower() == 'positive':
            response_text = f"That's great! I'm glad you're feeling {emotion.lower()}. Keep up the positive energy!"
        elif sentiment.lower() == 'negative':
            response_text = f"I hear that you're feeling {emotion.lower()}. It's completely normal to have difficult days. Is there anything specific that's been challenging lately?"
        else:
            response_text = f"I understand you're feeling {emotion.lower()}. How can I support you today?"
        
        response_data = {
            'sentiment': sentiment,
            'emotion': emotion,
            'flag': flag,
            'response': response_text
        }
        
        print(f"Sending response: {response_data}")
        return jsonify(response_data)
        
    except Exception as e:
        print(f"Error in prediction: {e}")
        print("Full traceback:")
        traceback.print_exc()
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({
        'status': 'healthy', 
        'model_loaded': predictor is not None,
        'model_path': os.path.join(os.path.dirname(_file_), 'Sentiment-Analysis', 'enhanced_sentiment_model.pkl')
    })

@app.route('/test', methods=['GET'])
def test_endpoint():
    return jsonify({'message': 'Server is running on port 8080!'})

@app.route('/reload', methods=['POST'])
def reload_model():
    """Reload the sentiment model"""
    global predictor
    success = load_sentiment_model()
    return jsonify({
        'success': success,
        'message': 'Model reloaded successfully' if success else 'Failed to reload model'
    })

if _name_ == '_main_':
    print("Starting sentiment analysis server...")
    print("Server will be available at http://localhost:8080")
    app.run(host='0.0.0.0', port=8080, debug=True)