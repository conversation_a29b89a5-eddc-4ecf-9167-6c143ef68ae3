import google.generativeai as genai
import json
import datetime
from fpdf import FPD<PERSON>
import os
from typing import List, Dict

class ChildAssessmentBot:
    def __init__(self, api_key: str):
        """Initialize the assessment bot with Gemini API"""
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
        self.conversation_history = []
        self.child_responses = []
        self.question_count = 0
        self.assessment_complete = False
        
        # Core assessment prompt for the AI
        self.system_prompt = """
        You are a friendly, supportive AI counselor conducting an assessment conversation with a child aged 13-16. 
        Your goal is to assess three key attributes: CONFIDENCE, LEADERSHIP, and CREATIVITY through natural conversation.
        
        ASSESSMENT GUIDELINES:
        1. Always start with "How are you feeling today?" and let the conversation flow naturally from there
        2. Ask follow-up questions based on their responses to keep the conversation engaging
        3. Gradually incorporate questions that reveal confidence, leadership, and creativity
        4. Ask at least 10 meaningful questions before concluding
        5. Be warm, encouraging, and age-appropriate
        6. Show genuine interest in their responses
        
        CONFIDENCE ASSESSMENT AREAS:
        - How they handle challenges and setbacks
        - Their willingness to try new things
        - How they feel about speaking up in class
        - Their self-perception and self-worth
        - How they handle mistakes or criticism
        
        LEADERSHIP ASSESSMENT AREAS:
        - How they work in groups
        - Whether they take initiative
        - How they help others
        - Their ability to make decisions
        - Whether they stand up for what they believe in
        
        CREATIVITY ASSESSMENT AREAS:
        - How they approach problem-solving
        - Their interests in arts, writing, or creative activities
        - How they think outside the box
        - Their imagination and original thinking
        - How they express themselves
        
        Keep responses conversational and under 2-3 sentences unless the child needs more detailed guidance.
        Remember their previous answers and reference them naturally in follow-up questions.
        
        Current conversation context: {context}
        """
    
    def start_conversation(self):
        """Start the assessment conversation"""
        print("🌟 Welcome to your daily check-in! 🌟")
        print("I'm here to chat with you and learn more about your day and experiences.")
        print("Let's have a friendly conversation!\n")
        
        # Always start with the required question
        first_question = "How are you feeling today?"
        print(f"AI: {first_question}")
        
        # Get the conversation started
        self.conduct_conversation()
    
    def conduct_conversation(self):
        """Main conversation loop"""
        context = ""
        
        while self.question_count < 10 and not self.assessment_complete:
            # Get user input
            user_response = input("\nYou: ").strip()
            
            if not user_response:
                print("AI: I'd love to hear your thoughts! Please share what's on your mind.")
                continue
            
            # Store the child's response
            self.child_responses.append({
                'question_number': self.question_count + 1,
                'response': user_response,
                'timestamp': datetime.datetime.now().isoformat()
            })
            
            # Update context with conversation history
            context = self.build_context()
            
            # Generate AI response using Gemini
            try:
                prompt = self.system_prompt.format(context=context) + f"\n\nChild's latest response: {user_response}\n\nProvide your next question or response (Question #{self.question_count + 1}):"
                
                response = self.model.generate_content(prompt)
                ai_response = response.text.strip()
                
                # Store conversation
                self.conversation_history.append({
                    'user': user_response,
                    'ai': ai_response,
                    'question_number': self.question_count + 1
                })
                
                print(f"\nAI: {ai_response}")
                self.question_count += 1
                
            except Exception as e:
                print(f"Error generating response: {e}")
                print("AI: I'm having trouble right now. Could you tell me more about that?")
        
        # After 10 questions, conclude and assess
        if self.question_count >= 10:
            self.conclude_conversation()
    
    def build_context(self) -> str:
        """Build context from conversation history"""
        context_parts = []
        for entry in self.conversation_history[-5:]:  # Last 5 exchanges for context
            context_parts.append(f"Child said: {entry['user']}")
            context_parts.append(f"AI responded: {entry['ai']}")
        return "\n".join(context_parts)
    
    def conclude_conversation(self):
        """Conclude the conversation and generate assessment"""
        print("\nAI: Thank you so much for sharing with me today! You've given me some wonderful insights.")
        print("Give me a moment to prepare a summary of our conversation...")
        
        # Generate assessment
        self.generate_assessment()
    
    def generate_assessment(self):
        """Generate detailed assessment using Gemini"""
        assessment_prompt = f"""
        Based on the following conversation with a child aged 13-16, provide a detailed assessment of their:
        1. CONFIDENCE level
        2. LEADERSHIP qualities  
        3. CREATIVITY traits
        
        Conversation data:
        {json.dumps(self.child_responses, indent=2)}
        
        For each attribute, provide:
        - A score from 1-10 (where 10 is highest)
        - 2-3 specific examples from their responses that support this score
        - 2-3 recommendations for growth in this area
        - Positive reinforcement highlighting their strengths
        
        Format the response as a structured assessment report that would be appropriate for parents/teachers.
        Be encouraging and constructive, focusing on growth opportunities rather than deficits.
        """
        
        try:
            response = self.model.generate_content(assessment_prompt)
            assessment_text = response.text.strip()
            
            # Generate reports
            self.save_assessment_report(assessment_text)
            print("\n✅ Assessment complete! Reports have been generated.")
            
        except Exception as e:
            print(f"Error generating assessment: {e}")
    
    def save_assessment_report(self, assessment_text: str):
        """Save assessment as both PDF and TXT"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save as TXT
        txt_filename = f"child_assessment_{timestamp}.txt"
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write("CHILD DEVELOPMENT ASSESSMENT REPORT\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("CONVERSATION RESPONSES:\n")
            f.write("-" * 30 + "\n\n")
            
            for i, response in enumerate(self.child_responses, 1):
                f.write(f"Response {i}: {response['response']}\n\n")
            
            f.write("\nASSESSMENT RESULTS:\n")
            f.write("-" * 30 + "\n\n")
            f.write(assessment_text)
        
        # Save as PDF
        try:
            self.create_pdf_report(assessment_text, timestamp)
        except Exception as e:
            print(f"PDF creation failed: {e}")
            print(f"TXT report saved as: {txt_filename}")
    
    def create_pdf_report(self, assessment_text: str, timestamp: str):
        """Create PDF report"""
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font("Arial", "B", 16)
        
        # Title
        pdf.cell(0, 10, "Child Development Assessment Report", ln=True, align='C')
        pdf.ln(5)
        
        pdf.set_font("Arial", size=12)
        pdf.cell(0, 10, f"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", ln=True)
        pdf.ln(5)
        
        # Conversation responses
        pdf.set_font("Arial", "B", 14)
        pdf.cell(0, 10, "Conversation Responses:", ln=True)
        pdf.set_font("Arial", size=10)
        
        for i, response in enumerate(self.child_responses, 1):
            pdf.ln(3)
            response_text = f"Q{i}: {response['response']}"
            # Handle text wrapping
            pdf.multi_cell(0, 5, response_text.encode('latin-1', 'replace').decode('latin-1'))
        
        pdf.ln(10)
        
        # Assessment results
        pdf.set_font("Arial", "B", 14)
        pdf.cell(0, 10, "Assessment Results:", ln=True)
        pdf.set_font("Arial", size=10)
        
        # Handle text wrapping for assessment
        assessment_lines = assessment_text.split('\n')
        for line in assessment_lines:
            if line.strip():
                pdf.multi_cell(0, 5, line.encode('latin-1', 'replace').decode('latin-1'))
        
        pdf_filename = f"child_assessment_{timestamp}.pdf"
        pdf.output(pdf_filename)
        print(f"Reports saved as: {pdf_filename} and child_assessment_{timestamp}.txt")

def main():
    """Main function to run the assessment"""
    print("🎯 Child Development Assessment Tool")
    print("=" * 40)
    
    # Get API key
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ API key is required to run this assessment.")
        return
    
    try:
        # Initialize and start assessment
        bot = ChildAssessmentBot(api_key)
        bot.start_conversation()
        
    except Exception as e:
        print(f"❌ Error initializing the assessment: {e}")
        print("Please check your API key and internet connection.")

if __name__ == "__main__":
    # Note: You'll need to install required packages:
    # pip install google-generativeai fpdf2
    main()