
import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import "./index.css";

import Starter from "./pages/Starter";
import Student from "./pages/student";
import ChatbotPage from "./pages/ChatHelp";
import Register from "./components/Register";

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-white">
        <Routes>
          <Route path="/" element={<Starter />} />
          <Route path="/student" element={<Student />} />
          <Route path="/chat" element={<ChatbotPage />} />
          <Route path="/register" element={<Register />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
